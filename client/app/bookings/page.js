'use client';
import React, { useState, useEffect } from 'react';
import { Calendar, Clock, AlertCircle, Trash2, Plus } from 'lucide-react';
import { useUserBookings, useCancelBooking } from '@/src/lib/hooks/useBookings';
import {
	BookingCard,
	BookingSearchFilter,
} from '@/src/components/features/booking';
import { useUserProfile, isAuthenticated } from '@/src/lib/hooks/useAuth';
import toast from 'react-hot-toast';
import Link from 'next/link';

const UserBookingsPage = () => {
	const [searchTerm, setSearchTerm] = useState('');
	const [filters, setFilters] = useState({
		status: '',
		sessionType: '',
		dateRange: '',
	});
	const [showCancelModal, setShowCancelModal] = useState(false);
	const [bookingToCancel, setBookingToCancel] = useState(null);

	// Get user profile
	const { data: userProfile, isLoading: isLoadingProfile } = useUserProfile();
	const user = userProfile?.data;

	// Get user bookings
	const {
		data: bookingsData,
		isLoading,
		error,
		refetch,
	} = useUserBookings(user?.email, {
		enabled: !!user?.email,
	});

	// Cancel booking mutation
	const cancelBookingMutation = useCancelBooking();

	// Filter bookings based on search and filters
	const filteredBookings = React.useMemo(() => {
		if (!bookingsData?.data) return [];

		let filtered = bookingsData.data;

		// Apply search filter
		if (searchTerm) {
			filtered = filtered.filter(
				(booking) =>
					booking.sessionType
						?.toLowerCase()
						.includes(searchTerm.toLowerCase()) ||
					booking.bookedBy?.toLowerCase().includes(searchTerm.toLowerCase()) ||
					booking.email?.toLowerCase().includes(searchTerm.toLowerCase()),
			);
		}

		// Apply status filter
		if (filters.status) {
			filtered = filtered.filter(
				(booking) => booking.status === filters.status,
			);
		}

		// Apply session type filter
		if (filters.sessionType) {
			filtered = filtered.filter(
				(booking) => booking.sessionType === filters.sessionType,
			);
		}

		// Apply date range filter
		if (filters.dateRange) {
			const now = new Date();
			filtered = filtered.filter((booking) => {
				const bookingDate = new Date(booking.date);
				switch (filters.dateRange) {
					case 'today':
						return bookingDate.toDateString() === now.toDateString();
					case 'week':
						const weekFromNow = new Date(
							now.getTime() + 7 * 24 * 60 * 60 * 1000,
						);
						return bookingDate >= now && bookingDate <= weekFromNow;
					case 'month':
						const monthFromNow = new Date(
							now.getFullYear(),
							now.getMonth() + 1,
							now.getDate(),
						);
						return bookingDate >= now && bookingDate <= monthFromNow;
					case 'past':
						return bookingDate < now;
					case 'upcoming':
						return bookingDate >= now;
					default:
						return true;
				}
			});
		}

		// Sort by date (upcoming first, then past)
		return filtered.sort((a, b) => new Date(b.date) - new Date(a.date));
	}, [bookingsData, searchTerm, filters]);

	const handleCancelBooking = (booking) => {
		setBookingToCancel(booking);
		setShowCancelModal(true);
	};

	const confirmCancelBooking = async () => {
		if (!bookingToCancel) return;

		try {
			await cancelBookingMutation.mutateAsync(bookingToCancel._id);
			toast.success('Booking cancelled successfully');
			setShowCancelModal(false);
			setBookingToCancel(null);
			refetch();
		} catch (error) {
			toast.error(error.message || 'Failed to cancel booking');
		}
	};

	const getBookingStats = () => {
		if (!bookingsData?.data)
			return { total: 0, upcoming: 0, completed: 0, cancelled: 0 };

		const now = new Date();
		return bookingsData.data.reduce(
			(stats, booking) => {
				stats.total++;
				const bookingDate = new Date(booking.date);

				if (booking.status === 'completed') {
					stats.completed++;
				} else if (booking.status === 'cancelled') {
					stats.cancelled++;
				} else if (bookingDate >= now) {
					stats.upcoming++;
				}

				return stats;
			},
			{ total: 0, upcoming: 0, completed: 0, cancelled: 0 },
		);
	};

	const stats = getBookingStats();

	// Check authentication
	if (!isAuthenticated() || isLoadingProfile) {
		return (
			<div className='min-h-screen flex items-center justify-center'>
				<div className='text-center'>
					{isLoadingProfile ? (
						<>
							<div className='w-8 h-8 border-2 border-teal-500 border-t-transparent rounded-full animate-spin mx-auto mb-4' />
							<p className='text-gray-600'>Loading...</p>
						</>
					) : (
						<>
							<AlertCircle className='w-16 h-16 text-gray-400 mx-auto mb-4' />
							<h2 className='text-2xl font-semibold text-gray-900 mb-2'>
								Please log in
							</h2>
							<p className='text-gray-600 mb-4'>
								You need to be logged in to view your bookings.
							</p>
							<Link
								href='/auth/login'
								className='bg-teal-500 text-white px-6 py-2 rounded-lg hover:bg-teal-600 transition-colors'>
								Log In
							</Link>
						</>
					)}
				</div>
			</div>
		);
	}

	if (!user) {
		return (
			<div className='min-h-screen flex items-center justify-center'>
				<div className='text-center'>
					<AlertCircle className='w-16 h-16 text-gray-400 mx-auto mb-4' />
					<h2 className='text-2xl font-semibold text-gray-900 mb-2'>
						Please log in
					</h2>
					<p className='text-gray-600 mb-4'>
						You need to be logged in to view your bookings.
					</p>
					<Link
						href='/auth/login'
						className='bg-teal-500 text-white px-6 py-2 rounded-lg hover:bg-teal-600 transition-colors'>
						Log In
					</Link>
				</div>
			</div>
		);
	}

	return (
		<div className='min-h-screen bg-gray-50 py-8'>
			<div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
				{/* Header */}
				<div className='mb-8'>
					<div className='flex justify-between items-center'>
						<div>
							<h1 className='text-3xl font-bold text-gray-900'>My Bookings</h1>
							<p className='text-gray-600 mt-1'>
								Manage your coaching session appointments
							</p>
						</div>
						<Link
							href='/session'
							className='bg-teal-500 text-white px-6 py-3 rounded-lg hover:bg-teal-600 transition-colors flex items-center space-x-2'>
							<Plus className='w-5 h-5' />
							<span>Book New Session</span>
						</Link>
					</div>
				</div>

				{/* Stats Cards */}
				<div className='grid grid-cols-1 md:grid-cols-4 gap-6 mb-8'>
					<div className='bg-white rounded-lg shadow p-6'>
						<div className='flex items-center'>
							<Calendar className='w-8 h-8 text-blue-500' />
							<div className='ml-4'>
								<p className='text-sm font-medium text-gray-600'>
									Total Bookings
								</p>
								<p className='text-2xl font-semibold text-gray-900'>
									{stats.total}
								</p>
							</div>
						</div>
					</div>
					<div className='bg-white rounded-lg shadow p-6'>
						<div className='flex items-center'>
							<Clock className='w-8 h-8 text-green-500' />
							<div className='ml-4'>
								<p className='text-sm font-medium text-gray-600'>Upcoming</p>
								<p className='text-2xl font-semibold text-gray-900'>
									{stats.upcoming}
								</p>
							</div>
						</div>
					</div>
					<div className='bg-white rounded-lg shadow p-6'>
						<div className='flex items-center'>
							<Calendar className='w-8 h-8 text-teal-500' />
							<div className='ml-4'>
								<p className='text-sm font-medium text-gray-600'>Completed</p>
								<p className='text-2xl font-semibold text-gray-900'>
									{stats.completed}
								</p>
							</div>
						</div>
					</div>
					<div className='bg-white rounded-lg shadow p-6'>
						<div className='flex items-center'>
							<Trash2 className='w-8 h-8 text-red-500' />
							<div className='ml-4'>
								<p className='text-sm font-medium text-gray-600'>Cancelled</p>
								<p className='text-2xl font-semibold text-gray-900'>
									{stats.cancelled}
								</p>
							</div>
						</div>
					</div>
				</div>

				{/* Search and Filter */}
				<div className='bg-white rounded-lg shadow p-6 mb-8'>
					<BookingSearchFilter
						onSearch={setSearchTerm}
						onFilter={setFilters}
						searchPlaceholder='Search your bookings...'
						showFilters={true}
					/>
				</div>

				{/* Bookings List */}
				<div className='space-y-6'>
					{isLoading && (
						<div className='text-center py-12'>
							<div className='w-8 h-8 border-2 border-teal-500 border-t-transparent rounded-full animate-spin mx-auto mb-4' />
							<p className='text-gray-600'>Loading your bookings...</p>
						</div>
					)}

					{error && (
						<div className='text-center py-12'>
							<AlertCircle className='w-16 h-16 text-red-400 mx-auto mb-4' />
							<h3 className='text-lg font-semibold text-gray-900 mb-2'>
								Failed to load bookings
							</h3>
							<p className='text-gray-600 mb-4'>
								There was an error loading your bookings.
							</p>
							<button
								onClick={() => refetch()}
								className='bg-teal-500 text-white px-6 py-2 rounded-lg hover:bg-teal-600 transition-colors'>
								Try Again
							</button>
						</div>
					)}

					{!isLoading && !error && filteredBookings.length === 0 && (
						<div className='text-center py-12'>
							<Calendar className='w-16 h-16 text-gray-400 mx-auto mb-4' />
							<h3 className='text-lg font-semibold text-gray-900 mb-2'>
								No bookings found
							</h3>
							<p className='text-gray-600 mb-4'>
								{searchTerm || Object.values(filters).some((f) => f)
									? 'No bookings match your search criteria.'
									: "You haven't made any bookings yet."}
							</p>
							<Link
								href='/session'
								className='bg-teal-500 text-white px-6 py-2 rounded-lg hover:bg-teal-600 transition-colors inline-flex items-center space-x-2'>
								<Plus className='w-5 h-5' />
								<span>Book Your First Session</span>
							</Link>
						</div>
					)}

					{!isLoading && !error && filteredBookings.length > 0 && (
						<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
							{filteredBookings.map((booking) => (
								<BookingCard
									key={booking._id}
									booking={booking}
									onCancel={handleCancelBooking}
									showActions={true}
									isUserView={true}
								/>
							))}
						</div>
					)}
				</div>
			</div>

			{/* Cancel Confirmation Modal */}
			{showCancelModal && (
				<div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50'>
					<div className='bg-white rounded-lg p-6 max-w-md w-full mx-4'>
						<h3 className='text-lg font-semibold text-gray-900 mb-4'>
							Cancel Booking
						</h3>
						<p className='text-gray-600 mb-6'>
							Are you sure you want to cancel this booking? This action cannot
							be undone.
						</p>
						<div className='flex justify-end space-x-4'>
							<button
								onClick={() => setShowCancelModal(false)}
								className='px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors'>
								Keep Booking
							</button>
							<button
								onClick={confirmCancelBooking}
								disabled={cancelBookingMutation.isLoading}
								className='px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors disabled:opacity-50'>
								{cancelBookingMutation.isLoading
									? 'Cancelling...'
									: 'Cancel Booking'}
							</button>
						</div>
					</div>
				</div>
			)}
		</div>
	);
};

export default UserBookingsPage;
