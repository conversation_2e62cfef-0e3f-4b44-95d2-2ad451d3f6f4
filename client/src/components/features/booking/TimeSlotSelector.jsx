import React, { useState, useMemo } from 'react';
import { Clock, AlertCircle } from 'lucide-react';

const TimeSlotSelector = ({ 
	selectedDate, 
	selectedTime, 
	onTimeSelect, 
	availableSlots = [], 
	isLoading = false, 
	error = null,
	className = '' 
}) => {
	const [selectedTimeOfDay, setSelectedTimeOfDay] = useState('morning');

	// Define time slots for different periods
	const timeSlots = {
		morning: ['09:00', '09:30', '10:00', '10:30', '11:00', '11:30'],
		afternoon: ['12:00', '12:30', '13:00', '13:30', '14:00', '14:30', '15:00', '15:30'],
		evening: ['16:00', '16:30', '17:00', '17:30', '18:00', '18:30', '19:00', '19:30']
	};

	// Get booked times for the selected date
	const bookedTimes = useMemo(() => {
		if (!selectedDate || !availableSlots?.data) return [];
		
		const selectedDateStr = selectedDate.toDateString();
		return availableSlots.data
			.filter(slot => {
				const slotDate = new Date(slot.date);
				return slotDate.toDateString() === selectedDateStr && slot.isBooked;
			})
			.map(slot => {
				const slotDate = new Date(slot.date);
				return slotDate.toLocaleTimeString('en-US', { 
					hour: '2-digit', 
					minute: '2-digit', 
					hour12: false 
				});
			});
	}, [selectedDate, availableSlots]);

	// Check if a time slot is available
	const isTimeSlotAvailable = (time) => {
		if (!selectedDate) return false;
		
		// Check if time is in the past
		const now = new Date();
		const [hours, minutes] = time.split(':');
		const slotDateTime = new Date(selectedDate);
		slotDateTime.setHours(parseInt(hours), parseInt(minutes), 0, 0);
		
		if (slotDateTime <= now) return false;
		
		// Check if time is already booked
		const timeIn24h = time;
		return !bookedTimes.includes(timeIn24h);
	};

	// Get available times for the current period
	const availableTimesForPeriod = useMemo(() => {
		return timeSlots[selectedTimeOfDay].filter(time => isTimeSlotAvailable(time));
	}, [selectedTimeOfDay, bookedTimes, selectedDate]);

	const TimeSlotButton = ({ time, isSelected, isDisabled, onClick }) => {
		const isPast = () => {
			if (!selectedDate) return false;
			const now = new Date();
			const [hours, minutes] = time.split(':');
			const slotDateTime = new Date(selectedDate);
			slotDateTime.setHours(parseInt(hours), parseInt(minutes), 0, 0);
			return slotDateTime <= now;
		};

		const isBooked = bookedTimes.includes(time);

		return (
			<button
				onClick={() => !isDisabled && onClick(time)}
				disabled={isDisabled}
				className={`
					p-3 rounded-lg border-2 font-medium transition-all text-sm
					${isSelected 
						? 'bg-teal-500 border-teal-500 text-white shadow-md' 
						: isDisabled
							? isPast()
								? 'bg-gray-100 border-gray-200 text-gray-400 cursor-not-allowed opacity-50'
								: isBooked
									? 'bg-red-50 border-red-200 text-red-400 cursor-not-allowed'
									: 'bg-gray-100 border-gray-200 text-gray-400 cursor-not-allowed'
							: 'bg-white border-gray-300 text-gray-700 hover:border-teal-300 hover:bg-teal-50'
					}
				`}
			>
				<div className="flex flex-col items-center space-y-1">
					<span>{time}</span>
					{isDisabled && (
						<span className="text-xs">
							{isPast() ? 'Past' : isBooked ? 'Booked' : 'N/A'}
						</span>
					)}
				</div>
			</button>
		);
	};

	if (!selectedDate) {
		return (
			<div className={`text-center py-8 ${className}`}>
				<Clock className="w-12 h-12 text-gray-300 mx-auto mb-2" />
				<p className="text-gray-500">Please select a date first</p>
			</div>
		);
	}

	return (
		<div className={`space-y-6 ${className}`}>
			<div className="text-center">
				<h3 className="text-xl font-semibold mb-2 flex items-center justify-center gap-2">
					<Clock size={24} className="text-teal-400" />
					Available Times
				</h3>
				<p className="text-gray-500">
					{selectedDate.toLocaleDateString('en-US', {
						weekday: 'long',
						year: 'numeric',
						month: 'long',
						day: 'numeric',
					})}
				</p>
			</div>

			{isLoading && (
				<div className="text-center py-8">
					<div className="w-8 h-8 border-2 border-teal-500 border-t-transparent rounded-full animate-spin mx-auto mb-2" />
					<p className="text-gray-500">Loading available slots...</p>
				</div>
			)}

			{error && (
				<div className="text-center py-8">
					<AlertCircle className="w-12 h-12 text-red-400 mx-auto mb-2" />
					<p className="text-red-500">Failed to load available slots</p>
					<p className="text-sm text-gray-500 mt-1">Please try again</p>
				</div>
			)}

			{!isLoading && !error && (
				<>
					{/* Time Period Selector */}
					<div className="flex justify-center mb-6">
						<div className="bg-gray-100 rounded-lg p-1 flex">
							{Object.keys(timeSlots).map((period) => (
								<button
									key={period}
									onClick={() => setSelectedTimeOfDay(period)}
									className={`px-4 py-2 rounded-lg font-medium transition-all capitalize ${
										selectedTimeOfDay === period
											? 'bg-teal-500 text-white shadow-sm'
											: 'text-gray-600 hover:text-gray-800'
									}`}
								>
									{period}
								</button>
							))}
						</div>
					</div>

					{/* Time Slots Grid */}
					<div className="grid grid-cols-2 md:grid-cols-3 gap-3">
						{timeSlots[selectedTimeOfDay].map((time) => (
							<TimeSlotButton
								key={time}
								time={time}
								isSelected={selectedTime === time}
								isDisabled={!isTimeSlotAvailable(time)}
								onClick={onTimeSelect}
							/>
						))}
					</div>

					{/* No Available Slots Message */}
					{availableTimesForPeriod.length === 0 && (
						<div className="text-center py-6 bg-gray-50 rounded-lg">
							<AlertCircle className="w-8 h-8 text-gray-400 mx-auto mb-2" />
							<p className="text-gray-600 font-medium">No available slots</p>
							<p className="text-sm text-gray-500">
								Try selecting a different time period or date
							</p>
						</div>
					)}

					{/* Legend */}
					<div className="flex justify-center space-x-6 text-sm">
						<div className="flex items-center space-x-2">
							<div className="w-3 h-3 bg-teal-500 rounded"></div>
							<span className="text-gray-600">Available</span>
						</div>
						<div className="flex items-center space-x-2">
							<div className="w-3 h-3 bg-red-200 rounded"></div>
							<span className="text-gray-600">Booked</span>
						</div>
						<div className="flex items-center space-x-2">
							<div className="w-3 h-3 bg-gray-200 rounded"></div>
							<span className="text-gray-600">Past</span>
						</div>
					</div>
				</>
			)}
		</div>
	);
};

export default TimeSlotSelector;
